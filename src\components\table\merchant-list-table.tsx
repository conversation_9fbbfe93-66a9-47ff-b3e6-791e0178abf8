"use client";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON>ontent,
} from "@/components/shadcn-ui/card";

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/shadcn-ui/table";
import React, { useState } from "react";
import { TableRowIdle, TableRowSkeleton } from "./table-row";
import AlertModal from "../ui/alert-modal";
import axios from "axios";
import { AlertBar, AlertProps } from "../ui/alert-bar";
import { IMerchantListByProject, MerchantListByProject } from "@/lib/types";
// Define the Merchant interface to match the API response
interface Merchant {
    merchantId: number;
    merchantCode: string;
    merchantName: string;
    slug: string;
    thirdPartyId: number;
    thirdPartyType: number;
    tokenKey: string | null;
}
import { admigLog } from "@/lib/actions";
import { object } from "zod";
import { Label } from "@/components/shadcn-ui/label";
import { Button } from "@/components/shadcn-ui/button";
import { Switch } from "@/components/shadcn-ui/switch";
import { ScrollArea, ScrollBar } from "@/components/shadcn-ui/scroll-area";
import FixedContent from "@/components/ui/fixed-content";

export default function MerchantConfigTable({
    isLoading,
    data,
    endpoint,
    session,
}: any ) {
    const [isAlertOpen, setIsAlertOpen] = useState(false);
    const [isLoadingModal, setIsLoadingModal] = useState(false);
    const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
    const [isLoadingTable, setIsLoadingTable] = useState(false);

    //**  Search Table List  */
    const [searchTerm, setSearchTerm] = useState("");

    //**  Checkbox Parameter  */
    const [isCheckAllActive, setIsCheckAllActive] = useState(false);
    const [isCheckAllInactive, setIsCheckAllInactive] = useState(false);


    //**  Filter and Render Merchant List */
    const filterData = (data: any) => {
        if (!data) return [];
        return Object.entries(data).filter(([key, item]) => {
            const searchLower = searchTerm.toLowerCase();
            return (
                (item as any)?.merchantId?.toString().toLowerCase().includes(searchLower) ||
                (item as any)?.merchantName?.toLowerCase().includes(searchLower) ||
                (item as any)?.slug?.toLowerCase().includes(searchLower)
            );
        });
    };

    //**  Render Merchant List */
    const rendermerchantlist = () => {
        if (!data) return null;
        const filteredData = filterData(data);
        if (filteredData.length === 0) {
            return (
                <TableRow>
                    <TableCell colSpan={4} className="text-center py-4">
                        No matching records found
                    </TableCell>
                </TableRow>
            );
        }
        return filteredData.map(([key, item], i) => (
            <React.Fragment key={i}>
                <TableRow>
                    <TableCell className="text-center">
                        <input 
                            type="checkbox" 
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        />
                    </TableCell>
                    <TableCell className="text-left">
                        {(item as any)?.merchantId || "N/A"}
                    </TableCell>
                    <TableCell className="text-left">
                        {(item as any)?.merchantName || "-"}
                    </TableCell>
                    <TableCell className="text-left">
                        {(item as any)?.slug || "-"}
                    </TableCell>
                </TableRow>
            </React.Fragment>
        ));
    };

    //**  Handle Search */
    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    //**  Handle Check All */
    const eventcheckall = () => {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        console.log(checkboxes)
        // checkboxes.forEach((checkbox) => {
        //     (checkbox as HTMLInputElement).checked = true;
        // });
    };

    return (
        <div className="grid grid-cols-12 gap-4">
            <Card className="col-span-12 lg:col-span-12">
                <CardHeader>
                    <CardTitle className="text-xl md:text-2xl">
                        รายการ Merchant
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-12 gap-4 mb-4">
                        <div className="col-span-6">
                            <Label htmlFor="search">ค้นหา Merchant</Label>
                            <input
                                type="text"
                                id="search"
                                value={searchTerm}
                                onChange={handleSearch}
                                placeholder="Search by Merchant ID, Name or Slug..."
                                className="w-full text-sm px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                            />
                        </div>
                        <div className="col-span-6 flex flex-row mt-6 justify-end gap-2">
                            <Button 
                                variant="default" 
                                className="bg-green-600 hover:bg-green-700"
                                onClick={() => {/* handle active */}}
                            >
                                Active
                            </Button>
                            <Button 
                                variant="default"
                                className="bg-red-600 hover:bg-red-700"
                                onClick={() => {/* handle inactive */}}
                            >
                                Inactive
                            </Button>
                        </div>
                    </div>
                    <ScrollArea className="mt-6 border whitespace-nowrap rounded-md">
                        <Table>
                            <TableHeader className="bg-default">
                                <TableRow>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center rounded-tl-md w-[5%]">
                                        <input type="checkbox" id="merchant-all" className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" />
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                                        MerchantId
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[20%] truncate">
                                        MerchantName
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                                        Slug
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {rendermerchantlist()}
                            </TableBody>
                            <ScrollBar orientation="horizontal" />
                        </Table>
                    </ScrollArea>
                </CardContent>
            </Card>
        </div>
    );
}